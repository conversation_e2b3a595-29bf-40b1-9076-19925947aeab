// ===== BASIC REACT COMPONENTS WITH TYPESCRIPT =====
// Learn how to create properly typed React components

import React from 'react';
import { User, Product } from '../types';

// 1. BASIC FUNCTIONAL COMPONENT WITH PROPS
// =========================================

interface WelcomeProps {
  name: string;
  age?: number; // Optional prop
}

export const Welcome: React.FC<WelcomeProps> = ({ name, age }) => {
  return (
    <div>
      <h2>Welcome, {name}!</h2>
      {age && <p>You are {age} years old.</p>}
    </div>
  );
};

// 2. COMPONENT WITH CHILDREN
// ===========================

interface CardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '' }) => {
  return (
    <div className={`card ${className}`}>
      <h3>{title}</h3>
      <div className="card-content">
        {children}
      </div>
    </div>
  );
};

// 3. COMPONENT WITH EVENT HANDLERS
// =================================

interface CounterProps {
  initialValue?: number;
  onCountChange?: (count: number) => void;
}

export const Counter: React.FC<CounterProps> = ({ 
  initialValue = 0, 
  onCountChange 
}) => {
  const [count, setCount] = React.useState<number>(initialValue);

  const handleIncrement = (): void => {
    const newCount = count + 1;
    setCount(newCount);
    onCountChange?.(newCount);
  };

  const handleDecrement = (): void => {
    const newCount = count - 1;
    setCount(newCount);
    onCountChange?.(newCount);
  };

  const handleReset = (): void => {
    setCount(initialValue);
    onCountChange?.(initialValue);
  };

  return (
    <div className="counter">
      <h3>Count: {count}</h3>
      <div className="counter-buttons">
        <button onClick={handleDecrement}>-</button>
        <button onClick={handleReset}>Reset</button>
        <button onClick={handleIncrement}>+</button>
      </div>
    </div>
  );
};

// 4. COMPONENT WITH COMPLEX PROPS
// ================================

interface UserCardProps {
  user: User;
  showEmail?: boolean;
  onEdit?: (user: User) => void;
  onDelete?: (userId: string) => void;
}

export const UserCard: React.FC<UserCardProps> = ({ 
  user, 
  showEmail = false, 
  onEdit, 
  onDelete 
}) => {
  const handleEdit = (): void => {
    onEdit?.(user);
  };

  const handleDelete = (): void => {
    if (window.confirm(`Are you sure you want to delete ${user.name}?`)) {
      onDelete?.(user.id);
    }
  };

  return (
    <div className="user-card">
      <div className="user-info">
        {user.avatar && <img src={user.avatar} alt={user.name} />}
        <h4>{user.name}</h4>
        {showEmail && <p>{user.email}</p>}
        <span className={`role ${user.role}`}>{user.role}</span>
        <span className={`status ${user.isActive ? 'active' : 'inactive'}`}>
          {user.isActive ? 'Active' : 'Inactive'}
        </span>
      </div>
      <div className="user-actions">
        {onEdit && <button onClick={handleEdit}>Edit</button>}
        {onDelete && <button onClick={handleDelete}>Delete</button>}
      </div>
    </div>
  );
};

// 5. COMPONENT WITH GENERIC PROPS
// ================================

interface ListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  emptyMessage?: string;
  className?: string;
}

export function List<T>({ 
  items, 
  renderItem, 
  emptyMessage = "No items found", 
  className = '' 
}: ListProps<T>): JSX.Element {
  if (items.length === 0) {
    return <div className={`list-empty ${className}`}>{emptyMessage}</div>;
  }

  return (
    <div className={`list ${className}`}>
      {items.map((item, index) => (
        <div key={index} className="list-item">
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  );
}

// 6. COMPONENT WITH CONDITIONAL RENDERING
// ========================================

interface LoadingStateProps {
  isLoading: boolean;
  error?: string | null;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  isLoading,
  error,
  children,
  loadingComponent = <div>Loading...</div>,
  errorComponent
}) => {
  if (isLoading) {
    return <>{loadingComponent}</>;
  }

  if (error) {
    return (
      <>
        {errorComponent || (
          <div className="error">
            <h3>Error</h3>
            <p>{error}</p>
          </div>
        )}
      </>
    );
  }

  return <>{children}</>;
};

// 7. COMPONENT WITH REF FORWARDING
// =================================

interface InputWithRefProps {
  label: string;
  placeholder?: string;
  type?: string;
}

export const InputWithRef = React.forwardRef<HTMLInputElement, InputWithRefProps>(
  ({ label, placeholder, type = 'text' }, ref) => {
    return (
      <div className="input-group">
        <label>{label}</label>
        <input
          ref={ref}
          type={type}
          placeholder={placeholder}
        />
      </div>
    );
  }
);

InputWithRef.displayName = 'InputWithRef';

// 8. HIGHER-ORDER COMPONENT (HOC)
// ================================

interface WithLoadingProps {
  isLoading: boolean;
}

export function withLoading<P extends object>(
  Component: React.ComponentType<P>
) {
  return (props: P & WithLoadingProps) => {
    const { isLoading, ...componentProps } = props;
    
    if (isLoading) {
      return <div>Loading...</div>;
    }
    
    return <Component {...(componentProps as P)} />;
  };
}

// Usage example:
const ProductCardWithLoading = withLoading(UserCard);
