// ===== TYPESCRIPT FUNDAMENTALS =====
// This file covers essential TypeScript concepts you'll use in React

// 1. BASIC TYPES
// ================

// Primitive types
const userName: string = "John Doe";
const age: number = 25;
const isActive: boolean = true;
const nothing: null = null;
const notDefined: undefined = undefined;

// Arrays
const numbers: number[] = [1, 2, 3, 4, 5];
const names: string[] = ["<PERSON>", "<PERSON>", "<PERSON>"];
const mixed: (string | number)[] = ["<PERSON>", 25, "<PERSON>", 30];

// 2. OBJECT TYPES & INTERFACES
// =============================

// Interface for a User object
interface User {
  id: number;
  name: string;
  email: string;
  age?: number; // Optional property
  isAdmin: boolean;
}

// Using the interface
const user1: User = {
  id: 1,
  name: "<PERSON>",
  email: "<EMAIL>",
  isAdmin: false
};

// Interface for a Product
interface Product {
  id: number;
  title: string;
  price: number;
  category: string;
  inStock: boolean;
  tags: string[];
}

// 3. FUNCTION TYPES
// =================

// Function with typed parameters and return type
function greetUser(name: string, age: number): string {
  return `Hello ${name}, you are ${age} years old!`;
}

// Arrow function with types
const calculateTotal = (price: number, tax: number): number => {
  return price + (price * tax);
};

// Function that returns a User object
const createUser = (name: string, email: string): User => {
  return {
    id: Date.now(),
    name,
    email,
    isAdmin: false
  };
};

// 4. UNION TYPES & LITERAL TYPES
// ===============================

// Union types (can be one of several types)
type Status = "loading" | "success" | "error";
type ID = string | number;

// Using union types
let currentStatus: Status = "loading";
let userId: ID = "user_123";

// Function with union type parameter
function handleResponse(status: Status): string {
  switch (status) {
    case "loading":
      return "Please wait...";
    case "success":
      return "Data loaded successfully!";
    case "error":
      return "Something went wrong!";
    default:
      return "Unknown status";
  }
}

// 5. GENERIC TYPES
// ================

// Generic function
function getFirstItem<T>(items: T[]): T | undefined {
  return items[0];
}

// Usage examples
const firstNumber = getFirstItem([1, 2, 3]); // Type: number | undefined
const firstName = getFirstItem(["Alice", "Bob"]); // Type: string | undefined

// Generic interface
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

// Usage
const userResponse: ApiResponse<User> = {
  data: user1,
  status: 200,
  message: "User fetched successfully"
};

// 6. TYPE ALIASES
// ===============

// Type alias for complex types
type UserWithProducts = User & {
  products: Product[];
  totalSpent: number;
};

// Event handler type (common in React)
type ButtonClickHandler = (event: MouseEvent) => void;
type InputChangeHandler = (event: Event) => void;

// 7. UTILITY TYPES (Very useful in React)
// ========================================

// Partial - makes all properties optional
type PartialUser = Partial<User>;

// Pick - select specific properties
type UserSummary = Pick<User, "id" | "name" | "email">;

// Omit - exclude specific properties
type CreateUserData = Omit<User, "id">;

// Record - create object type with specific keys
type UserRoles = Record<string, boolean>;

// 8. ENUMS
// ========

enum UserRole {
  ADMIN = "admin",
  USER = "user",
  MODERATOR = "moderator"
}

enum HttpStatus {
  OK = 200,
  NOT_FOUND = 404,
  SERVER_ERROR = 500
}

// Usage
const currentUserRole: UserRole = UserRole.USER;

// 9. CLASSES WITH TYPESCRIPT
// ===========================

class UserManager {
  private users: User[] = [];

  constructor(initialUsers: User[] = []) {
    this.users = initialUsers;
  }

  public addUser(user: User): void {
    this.users.push(user);
  }

  public getUserById(id: number): User | undefined {
    return this.users.find(user => user.id === id);
  }

  public getAllUsers(): User[] {
    return [...this.users]; // Return a copy
  }

  public getUserCount(): number {
    return this.users.length;
  }
}

// 10. EXPORT TYPES (for use in React components)
// ===============================================

export type { User, Product, Status, ID, ApiResponse, UserWithProducts };
export { UserRole, HttpStatus, UserManager };
export { greetUser, calculateTotal, createUser, handleResponse, getFirstItem };
