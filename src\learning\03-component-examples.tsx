// ===== COMPONENT USAGE EXAMPLES =====
// This file demonstrates how to use the components we've created

import React, { useState } from 'react';
import { Button } from '../components/Button';
import { Input, Textarea, Select } from '../components/Input';
import { Modal, ConfirmModal } from '../components/Modal';
import { User, UserRole } from '../types';

// Example: User Management Component
export const UserManagementExample: React.FC = () => {
  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: UserRole.USER,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: UserRole.USER,
    bio: ''
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Handle form input changes
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent): Promise<void> => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUser: User = {
        id: Date.now().toString(),
        name: formData.name,
        email: formData.email,
        role: formData.role,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setUsers(prev => [...prev, newUser]);
      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Failed to create user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = (): void => {
    setFormData({
      name: '',
      email: '',
      role: UserRole.USER,
      bio: ''
    });
    setFormErrors({});
  };

  const handleDeleteUser = (user: User): void => {
    setSelectedUser(user);
    setIsConfirmModalOpen(true);
  };

  const confirmDeleteUser = (): void => {
    if (selectedUser) {
      setUsers(prev => prev.filter(user => user.id !== selectedUser.id));
      setSelectedUser(null);
    }
  };

  const roleOptions = [
    { value: UserRole.USER, label: 'User' },
    { value: UserRole.ADMIN, label: 'Admin' },
    { value: UserRole.MODERATOR, label: 'Moderator' }
  ];

  return (
    <div className="user-management">
      <div className="header">
        <h1>User Management</h1>
        <Button
          variant="primary"
          onClick={() => setIsModalOpen(true)}
        >
          Add New User
        </Button>
      </div>

      {/* User List */}
      <div className="user-list">
        {users.map(user => (
          <div key={user.id} className="user-item">
            <div className="user-info">
              <h3>{user.name}</h3>
              <p>{user.email}</p>
              <span className={`role ${user.role}`}>{user.role}</span>
            </div>
            <div className="user-actions">
              <Button
                variant="secondary"
                size="small"
                onClick={() => console.log('Edit user:', user)}
              >
                Edit
              </Button>
              <Button
                variant="danger"
                size="small"
                onClick={() => handleDeleteUser(user)}
              >
                Delete
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Add User Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        title="Add New User"
        size="medium"
      >
        <form onSubmit={handleSubmit}>
          <Input
            name="name"
            label="Full Name"
            value={formData.name}
            onChange={handleInputChange}
            error={formErrors.name}
            required
            placeholder="Enter full name"
          />

          <Input
            name="email"
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            error={formErrors.email}
            required
            placeholder="Enter email address"
          />

          <Select
            name="role"
            label="User Role"
            value={formData.role}
            onChange={handleInputChange}
            options={roleOptions}
            required
          />

          <Textarea
            name="bio"
            label="Bio (Optional)"
            value={formData.bio}
            onChange={handleInputChange}
            placeholder="Enter user bio..."
            rows={3}
          />

          <div className="form-actions">
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                setIsModalOpen(false);
                resetForm();
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isLoading}
            >
              Create User
            </Button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={isConfirmModalOpen}
        onClose={() => {
          setIsConfirmModalOpen(false);
          setSelectedUser(null);
        }}
        onConfirm={confirmDeleteUser}
        title="Delete User"
        message={`Are you sure you want to delete ${selectedUser?.name}? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
      />
    </div>
  );
};

// Example: Counter with all button variants
export const ButtonShowcase: React.FC = () => {
  const [count, setCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const handleAsyncAction = async (): Promise<void> => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
    setCount(prev => prev + 10);
  };

  return (
    <div className="button-showcase">
      <h2>Button Showcase</h2>
      <p>Count: {count}</p>
      
      <div className="button-grid">
        <Button variant="primary" onClick={() => setCount(prev => prev + 1)}>
          Primary (+1)
        </Button>
        
        <Button variant="secondary" onClick={() => setCount(prev => prev - 1)}>
          Secondary (-1)
        </Button>
        
        <Button variant="success" onClick={() => setCount(0)}>
          Success (Reset)
        </Button>
        
        <Button variant="warning" onClick={() => setCount(prev => prev * 2)}>
          Warning (×2)
        </Button>
        
        <Button variant="danger" onClick={() => setCount(prev => Math.floor(prev / 2))}>
          Danger (÷2)
        </Button>
        
        <Button
          variant="primary"
          loading={isLoading}
          onClick={handleAsyncAction}
        >
          Async Action (+10)
        </Button>
        
        <Button variant="primary" size="small">
          Small Button
        </Button>
        
        <Button variant="primary" size="large">
          Large Button
        </Button>
        
        <Button variant="primary" fullWidth>
          Full Width Button
        </Button>
        
        <Button variant="primary" disabled>
          Disabled Button
        </Button>
      </div>
    </div>
  );
};

export default UserManagementExample;
