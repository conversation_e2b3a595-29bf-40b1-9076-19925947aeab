// ===== ADVANCED COMPONENT PATTERNS WITH TYPESCRIPT =====
// Learn advanced React patterns with proper TypeScript typing

import React, { createContext, useContext, useState, useCallback } from 'react';

// 1. COMPOUND COMPONENTS PATTERN
// ===============================

interface TabsContextType {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabsContext = createContext<TabsContextType | undefined>(undefined);

const useTabs = (): TabsContextType => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tab components must be used within a Tabs component');
  }
  return context;
};

interface TabsProps {
  defaultTab?: string;
  children: React.ReactNode;
  onChange?: (activeTab: string) => void;
}

const Tabs: React.FC<TabsProps> & {
  List: typeof TabList;
  Tab: typeof Tab;
  Panels: typeof TabPanels;
  Panel: typeof TabPanel;
} = ({ defaultTab = '', children, onChange }) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
    onChange?.(tab);
  }, [onChange]);

  const value: TabsContextType = {
    activeTab,
    setActiveTab: handleTabChange
  };

  return (
    <TabsContext.Provider value={value}>
      <div className="tabs">
        {children}
      </div>
    </TabsContext.Provider>
  );
};

interface TabListProps {
  children: React.ReactNode;
}

const TabList: React.FC<TabListProps> = ({ children }) => {
  return (
    <div className="tab-list" role="tablist">
      {children}
    </div>
  );
};

interface TabProps {
  value: string;
  children: React.ReactNode;
  disabled?: boolean;
}

const Tab: React.FC<TabProps> = ({ value, children, disabled = false }) => {
  const { activeTab, setActiveTab } = useTabs();
  const isActive = activeTab === value;

  const handleClick = (): void => {
    if (!disabled) {
      setActiveTab(value);
    }
  };

  return (
    <button
      className={`tab ${isActive ? 'tab-active' : ''} ${disabled ? 'tab-disabled' : ''}`}
      onClick={handleClick}
      disabled={disabled}
      role="tab"
      aria-selected={isActive}
    >
      {children}
    </button>
  );
};

interface TabPanelsProps {
  children: React.ReactNode;
}

const TabPanels: React.FC<TabPanelsProps> = ({ children }) => {
  return (
    <div className="tab-panels">
      {children}
    </div>
  );
};

interface TabPanelProps {
  value: string;
  children: React.ReactNode;
}

const TabPanel: React.FC<TabPanelProps> = ({ value, children }) => {
  const { activeTab } = useTabs();
  const isActive = activeTab === value;

  if (!isActive) {
    return null;
  }

  return (
    <div className="tab-panel" role="tabpanel">
      {children}
    </div>
  );
};

// Attach sub-components to main component
Tabs.List = TabList;
Tabs.Tab = Tab;
Tabs.Panels = TabPanels;
Tabs.Panel = TabPanel;

// 2. RENDER PROPS PATTERN
// ========================

interface DataFetcherProps<T> {
  url: string;
  children: (data: {
    data: T | null;
    loading: boolean;
    error: string | null;
    refetch: () => void;
  }) => React.ReactNode;
}

function DataFetcher<T>({ url, children }: DataFetcherProps<T>): JSX.Element {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [url]);

  React.useEffect(() => {
    fetchData();
  }, [fetchData]);

  return <>{children({ data, loading, error, refetch: fetchData })}</>;
}

// 3. CUSTOM HOOKS PATTERN
// ========================

interface UseToggleReturn {
  isOn: boolean;
  toggle: () => void;
  turnOn: () => void;
  turnOff: () => void;
}

export const useToggle = (initialValue: boolean = false): UseToggleReturn => {
  const [isOn, setIsOn] = useState(initialValue);

  const toggle = useCallback(() => {
    setIsOn(prev => !prev);
  }, []);

  const turnOn = useCallback(() => {
    setIsOn(true);
  }, []);

  const turnOff = useCallback(() => {
    setIsOn(false);
  }, []);

  return { isOn, toggle, turnOn, turnOff };
};

interface UseCounterReturn {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
  set: (value: number) => void;
}

export const useCounter = (initialValue: number = 0): UseCounterReturn => {
  const [count, setCount] = useState(initialValue);

  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);

  const decrement = useCallback(() => {
    setCount(prev => prev - 1);
  }, []);

  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);

  const set = useCallback((value: number) => {
    setCount(value);
  }, []);

  return { count, increment, decrement, reset, set };
};

// 4. POLYMORPHIC COMPONENTS
// ==========================

type AsProps<T extends React.ElementType> = {
  as?: T;
} & React.ComponentPropsWithoutRef<T>;

interface BoxOwnProps {
  children: React.ReactNode;
  className?: string;
}

type BoxProps<T extends React.ElementType = 'div'> = BoxOwnProps & AsProps<T>;

function Box<T extends React.ElementType = 'div'>({
  as,
  children,
  className = '',
  ...props
}: BoxProps<T>): JSX.Element {
  const Component = as || 'div';
  
  return (
    <Component className={`box ${className}`} {...props}>
      {children}
    </Component>
  );
}

// 5. CONTROLLED VS UNCONTROLLED COMPONENTS
// =========================================

interface ControlledInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
}

const ControlledInput: React.FC<ControlledInputProps> = ({
  value,
  onChange,
  placeholder,
  label
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    onChange(event.target.value);
  };

  return (
    <div className="input-group">
      {label && <label>{label}</label>}
      <input
        type="text"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
      />
    </div>
  );
};

interface UncontrolledInputProps {
  defaultValue?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  label?: string;
}

const UncontrolledInput: React.FC<UncontrolledInputProps> = ({
  defaultValue = '',
  onValueChange,
  placeholder,
  label
}) => {
  const [internalValue, setInternalValue] = useState(defaultValue);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const newValue = event.target.value;
    setInternalValue(newValue);
    onValueChange?.(newValue);
  };

  return (
    <div className="input-group">
      {label && <label>{label}</label>}
      <input
        type="text"
        value={internalValue}
        onChange={handleChange}
        placeholder={placeholder}
      />
    </div>
  );
};

// 6. EXAMPLE USAGE COMPONENT
// ===========================

export const AdvancedPatternsExample: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const { isOn, toggle } = useToggle();
  const { count, increment, decrement, reset } = useCounter(0);

  return (
    <div className="advanced-patterns-example">
      <h1>Advanced Component Patterns</h1>

      {/* Compound Components */}
      <section>
        <h2>Compound Components (Tabs)</h2>
        <Tabs defaultTab="tab1">
          <Tabs.List>
            <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
            <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
            <Tabs.Tab value="tab3">Tab 3</Tabs.Tab>
          </Tabs.List>
          <Tabs.Panels>
            <Tabs.Panel value="tab1">
              <p>Content for Tab 1</p>
            </Tabs.Panel>
            <Tabs.Panel value="tab2">
              <p>Content for Tab 2</p>
            </Tabs.Panel>
            <Tabs.Panel value="tab3">
              <p>Content for Tab 3</p>
            </Tabs.Panel>
          </Tabs.Panels>
        </Tabs>
      </section>

      {/* Render Props */}
      <section>
        <h2>Render Props (Data Fetcher)</h2>
        <DataFetcher<{ title: string; body: string }>
          url="https://jsonplaceholder.typicode.com/posts/1"
        >
          {({ data, loading, error, refetch }) => (
            <div>
              {loading && <p>Loading...</p>}
              {error && <p>Error: {error}</p>}
              {data && (
                <div>
                  <h3>{data.title}</h3>
                  <p>{data.body}</p>
                </div>
              )}
              <button onClick={refetch}>Refetch</button>
            </div>
          )}
        </DataFetcher>
      </section>

      {/* Custom Hooks */}
      <section>
        <h2>Custom Hooks</h2>
        <div>
          <p>Toggle: {isOn ? 'ON' : 'OFF'}</p>
          <button onClick={toggle}>Toggle</button>
        </div>
        <div>
          <p>Counter: {count}</p>
          <button onClick={increment}>+</button>
          <button onClick={decrement}>-</button>
          <button onClick={reset}>Reset</button>
        </div>
      </section>

      {/* Polymorphic Components */}
      <section>
        <h2>Polymorphic Components</h2>
        <Box>Default div box</Box>
        <Box as="section">Section box</Box>
        <Box as="button" onClick={() => alert('Clicked!')}>
          Button box
        </Box>
      </section>

      {/* Controlled vs Uncontrolled */}
      <section>
        <h2>Controlled vs Uncontrolled</h2>
        <ControlledInput
          label="Controlled Input"
          value={inputValue}
          onChange={setInputValue}
          placeholder="Type something..."
        />
        <p>Controlled value: {inputValue}</p>
        
        <UncontrolledInput
          label="Uncontrolled Input"
          defaultValue="Default value"
          onValueChange={(value) => console.log('Uncontrolled value:', value)}
          placeholder="Type something..."
        />
      </section>
    </div>
  );
};

export { Tabs, DataFetcher, Box, ControlledInput, UncontrolledInput };
