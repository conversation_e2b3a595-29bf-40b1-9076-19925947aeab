// ===== REUSABLE INPUT COMPONENT =====
import React from 'react';
import { InputProps } from '../types';

export const Input: React.FC<InputProps> = ({
  name,
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  required = false,
  disabled = false,
  ...rest
}) => {
  const inputId = `input-${name}`;
  const hasError = <PERSON>olean(error);
  
  const inputClasses = [
    'input',
    hasError ? 'input-error' : '',
    disabled ? 'input-disabled' : ''
  ].filter(Boolean).join(' ');

  return (
    <div className="input-group">
      {label && (
        <label htmlFor={inputId} className="input-label">
          {label}
          {required && <span className="required">*</span>}
        </label>
      )}
      
      <input
        id={inputId}
        name={name}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        disabled={disabled}
        className={inputClasses}
        aria-invalid={hasError}
        aria-describedby={hasError ? `${inputId}-error` : undefined}
        {...rest}
      />
      
      {error && (
        <span id={`${inputId}-error`} className="input-error-message">
          {error}
        </span>
      )}
    </div>
  );
};

// Textarea variant
interface TextareaProps extends Omit<InputProps, 'type'> {
  rows?: number;
  cols?: number;
  resize?: 'none' | 'both' | 'horizontal' | 'vertical';
}

export const Textarea: React.FC<TextareaProps> = ({
  name,
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  required = false,
  disabled = false,
  rows = 4,
  cols,
  resize = 'vertical',
  ...rest
}) => {
  const textareaId = `textarea-${name}`;
  const hasError = Boolean(error);
  
  const textareaClasses = [
    'textarea',
    hasError ? 'textarea-error' : '',
    disabled ? 'textarea-disabled' : ''
  ].filter(Boolean).join(' ');

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>): void => {
    // Convert textarea event to input event for consistency
    const inputEvent = {
      ...event,
      target: {
        ...event.target,
        type: 'textarea'
      }
    } as React.ChangeEvent<HTMLInputElement>;
    
    onChange(inputEvent);
  };

  const handleBlur = (event: React.FocusEvent<HTMLTextAreaElement>): void => {
    if (onBlur) {
      const inputEvent = {
        ...event,
        target: {
          ...event.target,
          type: 'textarea'
        }
      } as React.FocusEvent<HTMLInputElement>;
      
      onBlur(inputEvent);
    }
  };

  return (
    <div className="input-group">
      {label && (
        <label htmlFor={textareaId} className="input-label">
          {label}
          {required && <span className="required">*</span>}
        </label>
      )}
      
      <textarea
        id={textareaId}
        name={name}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        disabled={disabled}
        rows={rows}
        cols={cols}
        className={textareaClasses}
        style={{ resize }}
        aria-invalid={hasError}
        aria-describedby={hasError ? `${textareaId}-error` : undefined}
        {...rest}
      />
      
      {error && (
        <span id={`${textareaId}-error`} className="input-error-message">
          {error}
        </span>
      )}
    </div>
  );
};

// Select component
interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps extends Omit<InputProps, 'type' | 'placeholder'> {
  options: SelectOption[];
  placeholder?: string;
}

export const Select: React.FC<SelectProps> = ({
  name,
  label,
  value,
  onChange,
  onBlur,
  error,
  required = false,
  disabled = false,
  options,
  placeholder,
  ...rest
}) => {
  const selectId = `select-${name}`;
  const hasError = Boolean(error);
  
  const selectClasses = [
    'select',
    hasError ? 'select-error' : '',
    disabled ? 'select-disabled' : ''
  ].filter(Boolean).join(' ');

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>): void => {
    const inputEvent = {
      ...event,
      target: {
        ...event.target,
        type: 'select'
      }
    } as React.ChangeEvent<HTMLInputElement>;
    
    onChange(inputEvent);
  };

  const handleBlur = (event: React.FocusEvent<HTMLSelectElement>): void => {
    if (onBlur) {
      const inputEvent = {
        ...event,
        target: {
          ...event.target,
          type: 'select'
        }
      } as React.FocusEvent<HTMLInputElement>;
      
      onBlur(inputEvent);
    }
  };

  return (
    <div className="input-group">
      {label && (
        <label htmlFor={selectId} className="input-label">
          {label}
          {required && <span className="required">*</span>}
        </label>
      )}
      
      <select
        id={selectId}
        name={name}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        disabled={disabled}
        className={selectClasses}
        aria-invalid={hasError}
        aria-describedby={hasError ? `${selectId}-error` : undefined}
        {...rest}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
      
      {error && (
        <span id={`${selectId}-error`} className="input-error-message">
          {error}
        </span>
      )}
    </div>
  );
};

export default Input;
