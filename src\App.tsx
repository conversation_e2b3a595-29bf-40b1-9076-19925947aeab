import React, { useState } from 'react';
import { default as Navbar } from "./components/Navbar";
import { UserManagementExample, ButtonShowcase } from './learning/03-component-examples';
import { AdvancedPatternsExample } from './learning/04-advanced-patterns';

type ActiveSection = 'home' | 'components' | 'patterns' | 'user-management';

function App() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('home');

  const renderContent = (): React.ReactNode => {
    switch (activeSection) {
      case 'components':
        return <ButtonShowcase />;
      case 'patterns':
        return <AdvancedPatternsExample />;
      case 'user-management':
        return <UserManagementExample />;
      default:
        return (
          <div className="home-content">
            <h2>🚀 React TypeScript Mastery Project</h2>
            <p>Welcome to your comprehensive React TypeScript learning journey!</p>

            <div className="learning-sections">
              <div className="section-card">
                <h3>📚 What You'll Learn</h3>
                <ul>
                  <li>TypeScript fundamentals and advanced types</li>
                  <li>Properly typed React components</li>
                  <li>State management with TypeScript</li>
                  <li>Form handling and validation</li>
                  <li>API integration and async operations</li>
                  <li>Advanced React patterns</li>
                  <li>Best practices and real-world examples</li>
                </ul>
              </div>

              <div className="section-card">
                <h3>🛠️ Project Structure</h3>
                <ul>
                  <li><code>src/learning/</code> - Learning exercises and examples</li>
                  <li><code>src/components/</code> - Reusable UI components</li>
                  <li><code>src/types/</code> - TypeScript type definitions</li>
                  <li><code>src/utils/</code> - Utility functions</li>
                </ul>
              </div>

              <div className="section-card">
                <h3>🎯 Current Progress</h3>
                <div className="progress-item">
                  ✅ Phase 1: TypeScript Fundamentals
                </div>
                <div className="progress-item">
                  ✅ Phase 2: React Components with TypeScript
                </div>
                <div className="progress-item">
                  🔄 Phase 3: State Management & Hooks
                </div>
                <div className="progress-item">
                  ⏳ Phase 4: Forms & Event Handling
                </div>
                <div className="progress-item">
                  ⏳ Phase 5: API Integration
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="app">
      <Navbar />

      <nav className="section-nav">
        <button
          className={activeSection === 'home' ? 'active' : ''}
          onClick={() => setActiveSection('home')}
        >
          🏠 Home
        </button>
        <button
          className={activeSection === 'components' ? 'active' : ''}
          onClick={() => setActiveSection('components')}
        >
          🧩 Components
        </button>
        <button
          className={activeSection === 'patterns' ? 'active' : ''}
          onClick={() => setActiveSection('patterns')}
        >
          🎨 Patterns
        </button>
        <button
          className={activeSection === 'user-management' ? 'active' : ''}
          onClick={() => setActiveSection('user-management')}
        >
          👥 User Management
        </button>
      </nav>

      <main className="main-content">
        {renderContent()}
      </main>
    </div>
  );
}

export default App;
